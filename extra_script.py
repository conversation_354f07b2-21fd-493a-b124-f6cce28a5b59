# PlatformIO extra script to import image processing functionality
# Import the image processing module and trigger PlatformIO integration

try:
    # Import PlatformIO environment
    Import("env")

    # Import our image processing function
    from image_processing.preprocess_files import preprocess_files

    # Ensure image headers exist before any compilation
    def ensure_headers_exist(source, target, env):
        """Ensure image headers exist before compilation starts."""
        import os
        from pathlib import Path

        # Check if the master header exists
        master_header = Path("include/images/image_bitmaps.h")
        if not master_header.exists():
            print("Image headers not found, generating...")
            preprocess_files()

    # Run preprocessing immediately when the script loads
    ensure_headers_exist(None, None, env)

    # Add custom target for manual preprocessing
    env.AddCustomTarget(
        name="preprocess_files",
        dependencies=None,
        actions=[preprocess_files],
        title="Preprocess Images",
        description="Preprocess PNG files to C++ headers",
    )

    # Add cleanup function
    def cleanup_generated_files(source, target, env):
        """Clean up all generated files including headers, previews, and test images."""
        import os
        from pathlib import Path

        print("Cleaning up generated files...")

        # Clean up C++ headers
        headers_dir = Path("include/images")
        if headers_dir.exists():
            for header_file in headers_dir.glob("*.h"):
                header_file.unlink()
                print(f"Removed: {header_file}")

        # Clean up preview images from data-export
        data_export_dir = Path("data-export")
        if data_export_dir.exists():
            for preview_file in data_export_dir.glob("*_preview.png"):
                preview_file.unlink()
                print(f"Removed: {preview_file}")

        # Clean up preview images from include/images
        if headers_dir.exists():
            for preview_file in headers_dir.glob("*_preview.png"):
                preview_file.unlink()
                print(f"Removed: {preview_file}")

        # Clean up test images
        test_images = [
            data_export_dir / "test_bw.png",
            data_export_dir / "test_grayscale.png"
        ]
        for test_image in test_images:
            if test_image.exists():
                test_image.unlink()
                print(f"Removed: {test_image}")

        print("Cleanup completed!")

    # Add custom cleanup target
    env.AddCustomTarget(
        name="cleanup_images",
        dependencies=None,
        actions=[cleanup_generated_files],
        title="Cleanup Generated Files",
        description="Remove all generated C++ headers, preview images, and test images",
    )

except NameError:
    # If Import is not available, we're not in PlatformIO context
    print("Warning: Not running in PlatformIO context, skipping image preprocessing integration")
except ImportError as e:
    print(f"Error importing image processing module: {e}")